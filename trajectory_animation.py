import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_trajectory(filename):
    """加载轨迹数据"""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                coords = line.split()
                if len(coords) == 2:
                    x, y = float(coords[0]), float(coords[1])
                    data.append([x, y])
    return np.array(data)

def create_trajectory_animation():
    """创建轨迹动画"""
    # 加载数据
    user_trajectory = load_trajectory('user_trajectory.txt')
    uav_trajectory = load_trajectory('uav_trajectory.txt')
    
    print(f"用户轨迹点数: {len(user_trajectory)}")
    print(f"无人机轨迹点数: {len(uav_trajectory)}")
    
    # 确定坐标范围
    all_x = np.concatenate([user_trajectory[:, 0], uav_trajectory[:, 0]])
    all_y = np.concatenate([user_trajectory[:, 1], uav_trajectory[:, 1]])
    
    x_min, x_max = all_x.min() - 5, all_x.max() + 5
    y_min, y_max = all_y.min() - 5, all_y.max() + 5
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.set_xlim(x_min, x_max)
    ax.set_ylim(y_min, y_max)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X Coordinate', fontsize=12)
    ax.set_ylabel('Y Coordinate', fontsize=12)
    ax.set_title('UAV and User Trajectory Animation', fontsize=14, fontweight='bold')
    
    # 初始化轨迹线
    user_line, = ax.plot([], [], 'b-', linewidth=2, label='User Trajectory', alpha=0.7)
    uav_line, = ax.plot([], [], 'r-', linewidth=2, label='UAV Trajectory', alpha=0.7)
    
    # 初始化当前位置点
    user_point = Circle((0, 0), 1.5, color='blue', alpha=0.8)
    uav_point = Circle((0, 0), 1.5, color='red', alpha=0.8)
    ax.add_patch(user_point)
    ax.add_patch(uav_point)
    
    # 添加图例
    ax.legend(loc='upper right', fontsize=10)
    
    # 添加信息文本
    info_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, 
                       verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def animate(frame):
        """动画函数"""
        # 计算当前帧对应的数据点数量
        max_points = min(len(user_trajectory), len(uav_trajectory))
        current_points = min(frame + 1, max_points)
        
        if current_points > 0:
            # 更新轨迹线
            user_line.set_data(user_trajectory[:current_points, 0], 
                             user_trajectory[:current_points, 1])
            uav_line.set_data(uav_trajectory[:current_points, 0], 
                            uav_trajectory[:current_points, 1])
            
            # 更新当前位置点
            current_idx = current_points - 1
            user_point.center = (user_trajectory[current_idx, 0], 
                               user_trajectory[current_idx, 1])
            uav_point.center = (uav_trajectory[current_idx, 0], 
                              uav_trajectory[current_idx, 1])
            
            # 计算距离
            distance = np.sqrt((user_trajectory[current_idx, 0] - uav_trajectory[current_idx, 0])**2 + 
                             (user_trajectory[current_idx, 1] - uav_trajectory[current_idx, 1])**2)
            
            # 更新信息文本
            info_text.set_text(f'Time Step: {current_points}/{max_points}\n'
                              f'User Position: ({user_trajectory[current_idx, 0]:.1f}, {user_trajectory[current_idx, 1]:.1f})\n'
                              f'UAV Position: ({uav_trajectory[current_idx, 0]:.1f}, {uav_trajectory[current_idx, 1]:.1f})\n'
                              f'Distance: {distance:.1f}')
        
        return user_line, uav_line, user_point, uav_point, info_text
    
    # 创建动画
    max_frames = min(len(user_trajectory), len(uav_trajectory))
    anim = animation.FuncAnimation(fig, animate, frames=max_frames, 
                                 interval=200, blit=False, repeat=True)
    
    # 保存为GIF
    print("Generating GIF animation...")
    anim.save('trajectory_animation.gif', writer='pillow', fps=5, dpi=100)
    print("GIF animation saved as 'trajectory_animation.gif'")
    
    # 显示动画
    plt.tight_layout()
    plt.show()
    
    return anim

if __name__ == "__main__":
    # 检查文件是否存在
    if not os.path.exists('user_trajectory.txt'):
        print("Error: Cannot find user_trajectory.txt file")
        exit(1)
    if not os.path.exists('uav_trajectory.txt'):
        print("Error: Cannot find uav_trajectory.txt file")
        exit(1)
    
    # 创建动画
    animation_obj = create_trajectory_animation()
