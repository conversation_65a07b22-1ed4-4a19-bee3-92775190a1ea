
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
from collections import deque
import os
import matplotlib.pyplot as plt
import datetime
from tqdm import tqdm
import shutil

# ==================================================================================================
class UserMovement:
    """
    用户移动模型：带惯性的随机移动
    """
    def __init__(self):
        self.current_angle = np.random.uniform(0, 2*np.pi)
        self.current_speed = np.random.uniform(1, 3)

    def update_position(self, current_pos):
        # 方向惯性 + 随机扰动
        angle_noise = np.random.normal(0, 0.3)  # 标准差0.3弧度
        self.current_angle += angle_noise

        # 速度随机变化
        speed_noise = np.random.normal(0, 0.5)
        self.current_speed = np.clip(self.current_speed + speed_noise, 0.5, 3)

        # 计算移动距离（0.05秒内的移动）
        move_distance = self.current_speed * 0.05

        # 更新位置
        new_x = current_pos[0] + move_distance * np.cos(self.current_angle)
        new_y = current_pos[1] + move_distance * np.sin(self.current_angle)

        # 边界反弹
        if new_x <= 0 or new_x >= 200:
            self.current_angle = np.pi - self.current_angle
            new_x = np.clip(new_x, 0, 200)
        if new_y <= 0 or new_y >= 200:
            self.current_angle = -self.current_angle
            new_y = np.clip(new_y, 0, 200)

        return (new_x, new_y)

# ==================================================================================================
class UAVTrackingEnv:
    """
    ==================================================================
    无人机追踪环境类，用于模拟无人机追踪移动用户的强化学习环境。

    参数:
    path (str): 数据保存路径。
    """
    def __init__(self, path: str):
        # 环境参数
        self.area_size = 200  # 活动区域 200m × 200m
        self.uav_speed = 20  # 无人机速度 20m/s
        self.time_step = 0.05  # 时间步长 0.05秒
        self.visit_threshold = 5  # 访问阈值 5米
        self.max_steps = 2000  # 最大步数 (100秒)

        # 位置和状态
        self.uav_pos = np.array([50.0, 50.0])  # 无人机起始位置
        self.user_pos = None  # 用户位置
        self.prev_distance = None  # 上一步的距离
        self.step_count = 0

        # 用户移动模型
        self.user_movement = UserMovement()

        # 轨迹记录
        self.uav_trajectory = []
        self.user_trajectory = []

        # 环境维度
        self.state_dim = 6  # [x_uav, y_uav, dx_relative, dy_relative, dvx_user, dvy_user]
        self.action_dim = 1  # 航向角 θ

        # 保存路径
        self.path = path

        # 训练阶段控制
        self.training_stage = 1  # 1: 静止目标, 2: 慢速移动, 3: 正常速度
        self.episode_count = 0

    def _get_state(self) -> np.ndarray:
        """
        获取当前状态向量 (6维)
        state = [x_uav_norm, y_uav_norm, dx_relative_norm, dy_relative_norm, dvx_user_norm, dvy_user_norm]

        返回:
        np.ndarray: 归一化的状态向量
        """
        # 位置归一化到[-1, 1]
        x_uav_norm = (self.uav_pos[0] - 100) / 100
        y_uav_norm = (self.uav_pos[1] - 100) / 100

        # 相对位置
        dx_relative = self.user_pos[0] - self.uav_pos[0]
        dy_relative = self.user_pos[1] - self.uav_pos[1]
        dx_relative_norm = dx_relative / 200
        dy_relative_norm = dy_relative / 200

        # 用户速度（从移动模型获取）
        dvx_user = self.user_movement.current_speed * np.cos(self.user_movement.current_angle)
        dvy_user = self.user_movement.current_speed * np.sin(self.user_movement.current_angle)
        dvx_user_norm = dvx_user / 3
        dvy_user_norm = dvy_user / 3

        state = np.array([
            x_uav_norm, y_uav_norm,
            dx_relative_norm, dy_relative_norm,
            dvx_user_norm, dvy_user_norm
        ], dtype=np.float32)

        return state

    def reset(self) -> np.ndarray:
        """
        重置环境，返回初始状态。
        返回:
        np.ndarray: 初始状态。
        """
        self.episode_count += 1

        # 根据训练阶段设置用户初始位置
        self.uav_pos = np.array([50.0, 50.0])
        self.user_pos = np.array([
            np.random.uniform(80, 120),
            np.random.uniform(80, 120)
        ])

        # 重置用户移动模型
        self.user_movement = UserMovement()

        # 根据训练阶段调整用户移动速度
        if self.episode_count <= -1:  # 第一阶段：静止目标
            self.training_stage = 1
            self.user_movement.current_speed = 0
        elif self.episode_count <= -1:  # 第二阶段：慢速移动
            self.training_stage = 2
            self.user_movement.current_speed = np.random.uniform(0.5, 1.0)
        else:  # 第三阶段：正常速度
            self.training_stage = 3
            self.user_movement.current_speed = np.random.uniform(1.0, 3)

        # 重置计数器和轨迹
        self.step_count = 0
        self.uav_trajectory = [self.uav_pos.copy()]
        self.user_trajectory = [self.user_pos.copy()]
        self.prev_distance = self._calculate_distance()

        return self._get_state()

    def _calculate_distance(self):
        """计算无人机与用户之间的距离"""
        return np.linalg.norm(self.uav_pos - self.user_pos)

    def _execute_uav_action(self, action_angle):
        """
        执行无人机动作
        action_angle: 航向角 θ ∈ [-π, π]
        """
        # 计算速度分量
        vx = self.uav_speed * np.cos(action_angle)
        vy = self.uav_speed * np.sin(action_angle)

        # 计算新位置（时间步长0.05s，移动距离1m）
        new_x = self.uav_pos[0] + vx * self.time_step
        new_y = self.uav_pos[1] + vy * self.time_step

        # 边界处理
        new_x = np.clip(new_x, 0, self.area_size)
        new_y = np.clip(new_y, 0, self.area_size)

        return np.array([new_x, new_y])

    def step(self, action):
        """
        执行一步环境交互
        action: 航向角 θ ∈ [-π, π]
        """
        # 保存上一步距离
        prev_distance = self._calculate_distance()

        # 执行无人机动作
        self.uav_pos = self._execute_uav_action(action[0])

        # 更新用户位置（根据训练阶段）
        if self.training_stage > 1:  # 非静止阶段
            self.user_pos = np.array(self.user_movement.update_position(self.user_pos))

        # 记录轨迹
        self.uav_trajectory.append(self.uav_pos.copy())
        self.user_trajectory.append(self.user_pos.copy())

        # 计算当前距离
        current_distance = self._calculate_distance()

        # 计算奖励和终止条件
        reward = self._calculate_reward(prev_distance, current_distance)
        done = self._check_done(current_distance)

        self.step_count += 1
        self.prev_distance = current_distance

        return self._get_state(), reward, done

    def _calculate_reward(self, prev_distance, current_distance):
        """
        计算奖励函数

        参数:
        prev_distance: 上一步的距离
        current_distance: 当前距离

        返回:
        float: 奖励值
        """
        reward = 0

        # 1. 距离奖励（主要驱动力）
        distance_reward = 2.0 * (prev_distance - current_distance)
        reward += distance_reward

        # 2. 时间惩罚（鼓励快速完成）
        time_penalty = -0.1
        reward += time_penalty

        # 3. 成功奖励
        if current_distance <= self.visit_threshold:
            reward += 100

        # 4. 超时惩罚
        if self.step_count >= self.max_steps:
            reward -= 20

        # 5. 效率奖励（接近目标时给额外奖励）
        if current_distance < 20:
            reward += 1.0

        return reward

    def _check_done(self, current_distance):
        """
        检查是否终止

        参数:
        current_distance: 当前距离

        返回:
        bool: 是否终止
        """
        # 成功访问
        if current_distance <= self.visit_threshold:
            return True

        # 超时
        if self.step_count >= self.max_steps:
            return True

        return False

    def render(self, episode=0):
        """
        渲染当前环境状态，保存图像到指定路径。

        参数:
        episode (int): 当前的 episode 数。
        """
        plt.figure(figsize=(10, 8))

        # 绘制轨迹
        uav_traj = np.array(self.uav_trajectory)
        user_traj = np.array(self.user_trajectory)

        plt.plot(uav_traj[:, 0], uav_traj[:, 1], 'b-', linewidth=2, label='UAV Trajectory')
        plt.plot(user_traj[:, 0], user_traj[:, 1], 'r-', linewidth=2, label='User Trajectory')

        # 绘制起始和结束位置
        plt.plot(uav_traj[0, 0], uav_traj[0, 1], 'go', markersize=12, label='UAV Start')
        plt.plot(uav_traj[-1, 0], uav_traj[-1, 1], 'bs', markersize=12, label='UAV End')
        plt.plot(user_traj[0, 0], user_traj[0, 1], 'ro', markersize=12, label='User Start')
        plt.plot(user_traj[-1, 0], user_traj[-1, 1], 'rs', markersize=12, label='User End')

        # 绘制访问阈值圆圈
        circle = plt.Circle(user_traj[-1], self.visit_threshold, fill=False, color='orange', linestyle='--', linewidth=2)
        plt.gca().add_patch(circle)

        plt.title(f'Episode {episode} - Steps: {self.step_count} - Distance: {self._calculate_distance():.2f}m')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.legend()
        plt.xlim(0, self.area_size)
        plt.ylim(0, self.area_size)
        # plt.grid(True, alpha=0.3)
        # plt.axis('equal')

        # 保存图像
        plt.savefig(os.path.join(self.path, f'episode_{episode}_trajectory.png'), dpi=150, bbox_inches='tight')
        plt.close()

    def save_data(self, episode):
        """
        保存当前的轨迹数据到指定路径。
        """
        # 保存无人机轨迹
        np.savetxt(os.path.join(self.path, f'episode_{episode}_uav_trajectory.txt'),
                   np.array(self.uav_trajectory))

        # 保存用户轨迹
        np.savetxt(os.path.join(self.path, f'episode_{episode}_user_trajectory.txt'),
                   np.array(self.user_trajectory))

# ==================================================================================================

# 定义Actor网络，输入为状态，输出为动作（航向角）
class Actor(nn.Module):
    def __init__(self, state_dim=6, action_dim=1, hidden_dim=256):
        super(Actor, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc4 = nn.Linear(hidden_dim, action_dim)
        self.dropout = nn.Dropout(0.1)

        # 权重初始化
        self._initialize_weights()

    def _initialize_weights(self):
        nn.init.xavier_uniform_(self.fc1.weight)
        nn.init.xavier_uniform_(self.fc2.weight)
        nn.init.xavier_uniform_(self.fc3.weight)
        nn.init.xavier_uniform_(self.fc4.weight)

    def forward(self, state):
        x = torch.relu(self.fc1(state))
        x = torch.relu(self.fc2(x))
        x = torch.relu(self.fc3(x))
        x = self.dropout(x)
        x = torch.tanh(self.fc4(x)) * np.pi  # 输出[-π, π]

        return x

# 定义Critic网络，输入为状态和动作，输出为Q值
class Critic(nn.Module):
    def __init__(self, state_dim=6, action_dim=1, hidden_dim=256):
        super(Critic, self).__init__()
        self.fc1 = nn.Linear(state_dim + action_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc4 = nn.Linear(hidden_dim, 1)

        # 权重初始化
        self._initialize_weights()

    def _initialize_weights(self):
        nn.init.xavier_uniform_(self.fc1.weight)
        nn.init.xavier_uniform_(self.fc2.weight)
        nn.init.xavier_uniform_(self.fc3.weight)
        nn.init.xavier_uniform_(self.fc4.weight)

    def forward(self, state, action):
        x = torch.cat([state, action], dim=1)
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = torch.relu(self.fc3(x))
        x = self.fc4(x)
        return x

# 定义经验回放池
class ReplayBuffer:
    def __init__(self, max_size=10000):
        self.buffer = deque(maxlen=max_size)

    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        state, action, reward, next_state, done = zip(*random.sample(self.buffer, batch_size))
        return (np.array(state), np.array(action), np.array(reward), np.array(next_state), np.array(done))

    def size(self):
        return len(self.buffer)

# DDPG算法主体
class DDPG:
    def __init__(self, state_dim=6, action_dim=1, gamma=0.99, tau=0.005, lr_actor=1e-4, lr_critic=1e-3):
        self.actor = Actor(state_dim, action_dim).to(device)
        self.actor_target = Actor(state_dim, action_dim).to(device)
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr_actor)

        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = Critic(state_dim, action_dim).to(device)
        self.critic_target.load_state_dict(self.critic.state_dict())
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr_critic)

        self.gamma = gamma
        self.tau = tau
        self.train_mode = True

        # 噪声参数
        self.noise_std = 0.2
        self.noise_decay = 0.995

    def select_action(self, state, add_noise=True):
        state = torch.FloatTensor(state.reshape(1, -1)).to(device)
        action = self.actor(state).cpu().data.numpy().flatten()

        # 添加探索噪声
        if add_noise and self.train_mode:
            noise = np.random.normal(0, self.noise_std, size=action.shape)
            action = np.clip(action + noise, -np.pi, np.pi)

        return action

    def train(self, replay_buffer, batch_size=64):
        if replay_buffer.size() < batch_size:
            return

        state, action, reward, next_state, done = replay_buffer.sample(batch_size)

        state = torch.FloatTensor(state).to(device)
        action = torch.FloatTensor(action).to(device)
        reward = torch.FloatTensor(reward).to(device).view(-1, 1)
        next_state = torch.FloatTensor(next_state).to(device)
        done = torch.FloatTensor(done).to(device).view(-1, 1)

        # 更新Critic网络
        with torch.no_grad():
            target_action = self.actor_target(next_state)
            target_Q = self.critic_target(next_state, target_action)
            target_Q = reward + ((1 - done) * self.gamma * target_Q)

        current_Q = self.critic(state, action)
        critic_loss = nn.MSELoss()(current_Q, target_Q)

        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 1.0)
        self.critic_optimizer.step()

        # 更新Actor网络
        actor_loss = -self.critic(state, self.actor(state)).mean()

        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()

        # 软更新目标网络
        self._soft_update(self.critic, self.critic_target)
        self._soft_update(self.actor, self.actor_target)

        # 噪声衰减
        self.noise_std *= self.noise_decay
        self.noise_std = max(self.noise_std, 0.01)

    def _soft_update(self, local_model, target_model):
        """软更新目标网络"""
        for target_param, local_param in zip(target_model.parameters(), local_model.parameters()):
            target_param.data.copy_(self.tau * local_param.data + (1.0 - self.tau) * target_param.data)

    def load(self, actor_path, critic_path):
        self.actor.load_state_dict(torch.load(actor_path, map_location=device))
        self.critic.load_state_dict(torch.load(critic_path, map_location=device))
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.critic_target.load_state_dict(self.critic.state_dict())
    
    # 开启评估模式
    def eval_mode(self):
        self.train_mode=False
        self.actor.eval()
        self.critic.eval()
        self.actor_target.eval()
        self.critic_target.eval()

def evaluate_performance(env, ddpg, num_episodes=10):
    """评估模型性能"""
    ddpg.eval_mode()
    total_rewards = []
    success_count = 0
    total_steps = []

    for _ in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        steps = 0

        while not done:
            action = ddpg.select_action(state, add_noise=False)
            next_state, reward, done = env.step(action)
            episode_reward += reward
            state = next_state
            steps += 1

        total_rewards.append(episode_reward)
        total_steps.append(steps)

        # 检查是否成功（距离小于阈值）
        if env._calculate_distance() <= env.visit_threshold:
            success_count += 1

    avg_reward = np.mean(total_rewards)
    success_rate = success_count / num_episodes
    avg_steps = np.mean(total_steps)

    print(f"Evaluation - Avg Reward: {avg_reward:.2f}, Success Rate: {success_rate:.2f}, Avg Steps: {avg_steps:.1f}")
    return avg_reward, success_rate, avg_steps

# 训练DDPG模型
def train_ddpg(env, max_episodes, max_timesteps, best_score, path):
    state_dim = env.state_dim
    action_dim = env.action_dim

    ddpg = DDPG(state_dim, action_dim)
    # 定义加载模型的路径
    actor_path = os.path.join(path, "best_actor.pth")
    critic_path = os.path.join(path, "best_critic.pth")

    # 检查是否存在已训练好的模型
    if os.path.exists(actor_path) and os.path.exists(critic_path):
        print("Loading existing models...")
        ddpg.load(actor_path, critic_path)
        print("Models loaded successfully.")

    replay_buffer = ReplayBuffer(max_size=100000)
    all_scores = []
    success_rates = []

    # 训练参数
    batch_size = 64
    random_episodes = 100  # 纯随机探索的episode数

    for episode in (pbar := tqdm(range(max_episodes), desc="Training")):
        state = env.reset()
        done = False
        episode_reward = 0.0
        steps = 0

        while not done and steps < max_timesteps:
            steps += 1

            # 前期随机探索
            if episode < random_episodes:
                action = np.random.uniform(-np.pi, np.pi, size=(env.action_dim,))
            else:
                action = ddpg.select_action(state, add_noise=True)

            next_state, reward, done = env.step(action)
            replay_buffer.add(state, action, reward, next_state, done)

            state = next_state
            episode_reward += reward

            # 开始训练
            if episode >= random_episodes and replay_buffer.size() > batch_size:
                ddpg.train(replay_buffer, batch_size)

        # 记录分数
        all_scores.append(episode_reward)

        # 更新进度条
        pbar.set_description(f"Episode {episode}, Reward: {episode_reward:.2f}, Steps: {steps}")

        # 定期打印信息
        if episode % 50 == 0:
            print(f"\nEpisode {episode}, Reward: {episode_reward:.2f}, Steps: {steps}")
            print(f"Training Stage: {env.training_stage}, Distance: {env._calculate_distance():.2f}m")

        # 每100个episode评估一次性能
        if episode % 100 == 0 and episode > random_episodes:
            avg_reward, success_rate, avg_steps = evaluate_performance(env, ddpg, 10)
            success_rates.append(success_rate)

            # 保存评估结果
            with open(os.path.join(path, 'evaluation_results.txt'), 'a') as f:
                f.write(f'{episode},{avg_reward:.2f},{success_rate:.2f},{avg_steps:.1f}\n')

        # 保存训练分数
        with open(os.path.join(path, 'training_scores.txt'), 'a') as f:
            f.write(f'{episode},{episode_reward:.2f},{steps}\n')

        # 检查是否需要保存最佳模型
        avg_score = np.mean(all_scores[-20:]) if len(all_scores) >= 20 else np.mean(all_scores)
        if avg_score > best_score:
            best_score = avg_score
            print(f"\n{'='*50}")
            print(f"New best average score: {avg_score:.2f}")
            print(f"Episode {episode}, Steps: {steps}")

            # 保存当前的最佳模型
            torch.save(ddpg.actor.state_dict(), os.path.join(path, "best_actor.pth"))
            torch.save(ddpg.critic.state_dict(), os.path.join(path, "best_critic.pth"))
            print("Best model saved!")

            # 渲染轨迹
            env.render(episode)
            env.save_data(episode)
            print(f"{'='*50}\n")

    # 保存最终模型
    torch.save(ddpg.actor.state_dict(), os.path.join(path, "final_actor.pth"))
    torch.save(ddpg.critic.state_dict(), os.path.join(path, "final_critic.pth"))
    print("Final model saved!")

    return ddpg, all_scores, success_rates

# 验证模型的性能
def validate_ddpg(env, max_timesteps, path, eval_episodes):
    state_dim = env.state_dim
    action_dim = env.action_dim

    ddpg = DDPG(state_dim, action_dim)

    # 加载已训练的模型
    actor_path = os.path.join(path, "best_actor.pth")
    critic_path = os.path.join(path, "best_critic.pth")

    if not os.path.exists(actor_path) or not os.path.exists(critic_path):
        print("No trained model found!")
        return

    ddpg.load(actor_path, critic_path)
    ddpg.eval_mode()

    results = []
    success_count = 0

    for episode in range(eval_episodes):
        print(f"\n{'='*40} Episode {episode+1} {'='*40}")

        state = env.reset()
        done = False
        steps = 0
        episode_reward = 0.0

        print(f"Initial - UAV: ({env.uav_pos[0]:.1f}, {env.uav_pos[1]:.1f}), "
              f"User: ({env.user_pos[0]:.1f}, {env.user_pos[1]:.1f}), "
              f"Distance: {env._calculate_distance():.2f}m")

        while not done and steps < max_timesteps:
            steps += 1
            action = ddpg.select_action(state, add_noise=False)
            next_state, reward, done = env.step(action)
            state = next_state
            episode_reward += reward

            if steps % 50 == 0 or done:
                print(f"Step {steps} - UAV: ({env.uav_pos[0]:.1f}, {env.uav_pos[1]:.1f}), "
                      f"User: ({env.user_pos[0]:.1f}, {env.user_pos[1]:.1f}), "
                      f"Distance: {env._calculate_distance():.2f}m, Reward: {reward:.2f}")

        final_distance = env._calculate_distance()
        is_success = final_distance <= env.visit_threshold

        if is_success:
            success_count += 1
            print(f"✓ SUCCESS! Final distance: {final_distance:.2f}m")
        else:
            print(f"✗ FAILED! Final distance: {final_distance:.2f}m")

        print(f"Episode {episode+1} completed - Steps: {steps}, Reward: {episode_reward:.2f}")

        results.append({
            'episode': episode + 1,
            'steps': steps,
            'reward': episode_reward,
            'final_distance': final_distance,
            'success': is_success
        })

        # 保存轨迹图
        env.render(f"validation_{episode+1}")
        env.save_data(f"validation_{episode+1}")

    # 打印总结
    success_rate = success_count / eval_episodes
    avg_steps = np.mean([r['steps'] for r in results])
    avg_reward = np.mean([r['reward'] for r in results])

    print(f"\n{'='*80}")
    print(f"VALIDATION SUMMARY")
    print(f"{'='*80}")
    print(f"Episodes: {eval_episodes}")
    print(f"Success Rate: {success_rate:.2%} ({success_count}/{eval_episodes})")
    print(f"Average Steps: {avg_steps:.1f}")
    print(f"Average Reward: {avg_reward:.2f}")
    print(f"{'='*80}")

    return results

def find_valid_path(dirs, folder_name):
    for idx, path in enumerate(dirs):
        parts = path.split("/")
        drive = parts[0] + "/"  # 盘符，例如 'D:'
        if os.path.exists(drive):  # 检查盘符是否存在
            current_path = drive
            for folder in parts[1:]:  # 依次检查文件夹
                current_path = os.path.join(current_path, folder)
                if os.path.exists(current_path):  # 如果文件夹不存在，跳出当前路径检查
                    return dirs[idx] + folder_name

def remove_file(path):
    # 判断路径是否存在
    if os.path.exists(path):
        # 遍历路径下的所有文件和文件夹
        for filename in os.listdir(path):
            # 构建完整的文件或文件夹路径
            file_path = os.path.join(path, filename)
            # 如果是文件，则删除文件
            if os.path.isfile(file_path):
                os.remove(file_path)  # 删除文件
            # 如果是文件夹，则递归删除文件夹及其内容
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # 递归删除文件夹及其内容

#创建文件夹
def create_path():
    folder_name = datetime.datetime.now().strftime("%Y-%m-%d/")
    dirs = [
        "D:/Desktop/临时文件夹/5/",
        "F:/实验数据/Month_7/",
        "F:/cluo/MARL/",
        "/home/<USER>/data/",
        "/content/work/"
    ]

    data_dir = find_valid_path(dirs, folder_name)
    print("当前文件夹路径：", data_dir)
    # 如果文件夹不存在则创建
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    return data_dir


def copy_file(target_directory):        
    current_file_path = os.path.abspath(__file__)  # 使用os.path.abspath获取当前文件的绝对路径        
    target_file_path = os.path.join(target_directory, os.path.basename(current_file_path))  # 组合目标路径和文件名        
    shutil.copy2(current_file_path, target_file_path)  # 复制文件并保留元数据       
    return target_file_path


if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 创建数据保存目录
    data_dir = create_path()

    # 训练参数
    max_episodes = 5000
    max_timesteps = 2000  # 100秒 * 20步/秒
    best_score = -float('inf')

    # 创建环境
    env = UAVTrackingEnv(data_dir)

    # 训练模式开关
    # train_mode = True
    train_mode = False

    if train_mode:
        print("Starting training...")
        print(f"Environment: {env.area_size}m x {env.area_size}m")
        print(f"Max episodes: {max_episodes}")
        print(f"Max timesteps per episode: {max_timesteps}")
        print(f"Visit threshold: {env.visit_threshold}m")
        print(f"Data directory: {data_dir}")

        # 清空文件夹并复制代码
        remove_file(data_dir)
        copy_file(data_dir)

        # 创建评估结果文件头
        with open(os.path.join(data_dir, 'evaluation_results.txt'), 'w') as f:
            f.write('episode,avg_reward,success_rate,avg_steps\n')
        with open(os.path.join(data_dir, 'training_scores.txt'), 'w') as f:
            f.write('episode,reward,steps\n')

        # 开始训练
        ddpg, scores, success_rates = train_ddpg(env, max_episodes, max_timesteps, best_score, data_dir)

        print("Training completed!")

    else:
        print("Starting validation...")
        # 运行验证
        results = validate_ddpg(env, max_timesteps, data_dir, 10)

        # 保存验证结果

        with open(os.path.join(data_dir, 'validation_results.txt'), 'w') as f:
            f.write('episode,steps,reward,final_distance,success\n')
            for r in results:
                f.write(f"{r['episode']},{r['steps']},{r['reward']:.2f},{r['final_distance']:.2f},{r['success']}\n")

        print("Validation completed!")