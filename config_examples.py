"""
配置参数示例
============

这个文件展示了如何调整 enhanced_trajectory_animation.py 中的各种参数。
复制需要的配置到主文件的配置区域即可。

使用方法：
1. 从下面选择合适的配置
2. 复制到 enhanced_trajectory_animation.py 的配置参数区域
3. 运行脚本查看效果
"""

# ==================== 快速动画配置 ====================
# 适用于快速预览，动画速度较快
QUICK_PREVIEW_CONFIG = {
    'ANIMATION_INTERVAL': 100,  # 更快的动画
    'ANIMATION_FPS': 10,        # 更高的帧率
    'ANIMATION_DPI': 80,        # 较低的分辨率以加快生成
}

# ==================== 高质量配置 ====================
# 适用于最终展示，高质量输出
HIGH_QUALITY_CONFIG = {
    'ANIMATION_INTERVAL': 300,  # 较慢的动画，便于观察细节
    'ANIMATION_FPS': 8,         # 适中的帧率
    'ANIMATION_DPI': 150,       # 高分辨率
    'LINE_WIDTH': 3,            # 更粗的线条
    'POINT_RADIUS': 3,          # 更大的点
}

# ==================== 自定义颜色主题 ====================
# 深色主题
DARK_THEME_CONFIG = {
    'USER_LINE_COLOR': 'cyan',
    'UAV_LINE_COLOR': 'orange',
    'CONNECTION_LINE_COLOR': 'yellow',
    'DISTANCE_LINE_COLOR': 'magenta',
    'USER_POINT_COLOR': 'cyan',
    'UAV_POINT_COLOR': 'orange',
    'INFO_BOX_COLOR': 'darkblue',
    'STATS_BOX_COLOR': 'darkgreen',
    'HISTOGRAM_COLOR': 'gold',
}

# 彩虹主题
RAINBOW_THEME_CONFIG = {
    'USER_LINE_COLOR': 'deeppink',
    'UAV_LINE_COLOR': 'lime',
    'CONNECTION_LINE_COLOR': 'gold',
    'DISTANCE_LINE_COLOR': 'darkorange',
    'USER_POINT_COLOR': 'deeppink',
    'UAV_POINT_COLOR': 'lime',
    'INFO_BOX_COLOR': 'lightpink',
    'STATS_BOX_COLOR': 'lightgreen',
    'HISTOGRAM_COLOR': 'coral',
}

# 专业主题（黑白灰）
PROFESSIONAL_THEME_CONFIG = {
    'USER_LINE_COLOR': 'black',
    'UAV_LINE_COLOR': 'gray',
    'CONNECTION_LINE_COLOR': 'lightgray',
    'DISTANCE_LINE_COLOR': 'darkgray',
    'USER_POINT_COLOR': 'black',
    'UAV_POINT_COLOR': 'gray',
    'INFO_BOX_COLOR': 'lightgray',
    'STATS_BOX_COLOR': 'whitesmoke',
    'HISTOGRAM_COLOR': 'silver',
}

# ==================== 不同文件路径配置 ====================
# 如果您的轨迹文件在不同位置或有不同名称
CUSTOM_FILES_CONFIG = {
    'USER_TRAJECTORY_FILE': 'data/user_path.txt',
    'UAV_TRAJECTORY_FILE': 'data/drone_path.txt',
    'OUTPUT_GIF_FILE': 'results/animation.gif',
    'OUTPUT_ANALYSIS_FILE': 'results/analysis.png',
}

# ==================== 大图形配置 ====================
# 适用于大屏幕展示或打印
LARGE_DISPLAY_CONFIG = {
    'FIGURE_SIZE_ANIMATION': (20, 10),
    'FIGURE_SIZE_ANALYSIS': (20, 15),
    'FONT_SIZE_TITLE': 18,
    'FONT_SIZE_LABEL': 14,
    'FONT_SIZE_LEGEND': 12,
    'FONT_SIZE_INFO': 12,
    'LINE_WIDTH': 3,
    'POINT_RADIUS': 4,
    'MARKER_SIZE': 150,
}

# ==================== 小图形配置 ====================
# 适用于嵌入文档或网页
SMALL_DISPLAY_CONFIG = {
    'FIGURE_SIZE_ANIMATION': (12, 6),
    'FIGURE_SIZE_ANALYSIS': (12, 9),
    'FONT_SIZE_TITLE': 10,
    'FONT_SIZE_LABEL': 8,
    'FONT_SIZE_LEGEND': 7,
    'FONT_SIZE_INFO': 7,
    'LINE_WIDTH': 1.5,
    'POINT_RADIUS': 1.5,
    'MARKER_SIZE': 60,
}

# ==================== 慢动画配置 ====================
# 适用于详细分析，动画很慢
SLOW_ANALYSIS_CONFIG = {
    'ANIMATION_INTERVAL': 500,  # 很慢的动画
    'ANIMATION_FPS': 3,         # 低帧率
    'DECIMAL_PLACES': 3,        # 更多小数位
}

# ==================== 快速动画配置 ====================
# 适用于快速浏览
FAST_OVERVIEW_CONFIG = {
    'ANIMATION_INTERVAL': 50,   # 很快的动画
    'ANIMATION_FPS': 15,        # 高帧率
    'DECIMAL_PLACES': 1,        # 较少小数位
}

# ==================== 使用示例 ====================
"""
要使用这些配置，请按以下步骤操作：

1. 选择一个配置，例如 HIGH_QUALITY_CONFIG
2. 在 enhanced_trajectory_animation.py 中找到对应的参数
3. 替换参数值

例如，要使用高质量配置：
# 在 enhanced_trajectory_animation.py 中修改：
ANIMATION_INTERVAL = 300  # 而不是 200
ANIMATION_FPS = 8         # 而不是 5
ANIMATION_DPI = 150       # 而不是 100
LINE_WIDTH = 3            # 而不是 2
POINT_RADIUS = 3          # 而不是 2

或者要使用深色主题：
USER_LINE_COLOR = 'cyan'     # 而不是 'blue'
UAV_LINE_COLOR = 'orange'    # 而不是 'red'
# ... 等等

您也可以混合使用不同配置的参数来创建自定义设置。
"""

# ==================== 常用参数说明 ====================
"""
主要参数说明：

动画控制：
- ANIMATION_INTERVAL: 帧间隔（毫秒），越小越快
- ANIMATION_FPS: GIF帧率，影响文件大小和播放流畅度
- ANIMATION_DPI: 分辨率，越高图像越清晰但文件越大

视觉效果：
- LINE_WIDTH: 线条粗细
- POINT_RADIUS: 当前位置点的大小
- LINE_ALPHA: 线条透明度（0-1）
- GRID_ALPHA: 网格透明度（0-1）

颜色设置：
- 支持matplotlib的所有颜色名称
- 可以使用十六进制颜色代码，如 '#FF5733'
- 常用颜色：'red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan'

文件设置：
- 可以使用相对路径或绝对路径
- 支持不同的文件格式（.txt, .csv等）
"""
