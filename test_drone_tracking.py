#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDPG无人机单目标追踪 - 测试版本
简化版本用于快速测试和验证功能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
from collections import deque
import os
import matplotlib.pyplot as plt
import datetime
from tqdm import tqdm

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

class UserMovement:
    """用户移动模型：带惯性的随机移动"""
    def __init__(self):
        self.current_angle = np.random.uniform(0, 2*np.pi)
        self.current_speed = np.random.uniform(1, 3)
        
    def update_position(self, current_pos):
        # 方向惯性 + 随机扰动
        angle_noise = np.random.normal(0, 0.3)
        self.current_angle += angle_noise
        
        # 速度随机变化
        speed_noise = np.random.normal(0, 0.5)
        self.current_speed = np.clip(self.current_speed + speed_noise, 0.5, 3)
        
        # 计算移动距离（0.05秒内的移动）
        move_distance = self.current_speed * 0.05
        
        # 更新位置
        new_x = current_pos[0] + move_distance * np.cos(self.current_angle)
        new_y = current_pos[1] + move_distance * np.sin(self.current_angle)
        
        # 边界反弹
        if new_x <= 0 or new_x >= 200:
            self.current_angle = np.pi - self.current_angle
            new_x = np.clip(new_x, 0, 200)
        if new_y <= 0 or new_y >= 200:
            self.current_angle = -self.current_angle  
            new_y = np.clip(new_y, 0, 200)
            
        return (new_x, new_y)

class DroneTrackingEnv:
    """无人机追踪环境类"""
    def __init__(self):
        # 环境参数
        self.area_size = 200
        self.drone_speed = 20
        self.time_step = 0.05
        self.visit_threshold = 10
        self.max_steps = 500  # 减少最大步数用于测试
        
        # 位置和状态
        self.drone_pos = np.array([50.0, 50.0])
        self.user_pos = None
        self.step_count = 0
        
        # 用户移动模型
        self.user_movement = UserMovement()
        
        # 轨迹记录
        self.drone_trajectory = []
        self.user_trajectory = []
        
        # 环境维度
        self.state_dim = 6
        self.action_dim = 1
        
    def _get_state(self):
        """获取当前状态向量"""
        # 位置归一化到[-1, 1]
        x_drone_norm = (self.drone_pos[0] - 100) / 100
        y_drone_norm = (self.drone_pos[1] - 100) / 100
        
        # 相对位置
        dx_relative = self.user_pos[0] - self.drone_pos[0]
        dy_relative = self.user_pos[1] - self.drone_pos[1]
        dx_relative_norm = dx_relative / 200
        dy_relative_norm = dy_relative / 200
        
        # 用户速度
        dvx_user = self.user_movement.current_speed * np.cos(self.user_movement.current_angle)
        dvy_user = self.user_movement.current_speed * np.sin(self.user_movement.current_angle)
        dvx_user_norm = dvx_user / 3
        dvy_user_norm = dvy_user / 3
        
        state = np.array([
            x_drone_norm, y_drone_norm,
            dx_relative_norm, dy_relative_norm,
            dvx_user_norm, dvy_user_norm
        ], dtype=np.float32)
        
        return state

    def reset(self):
        """重置环境"""
        self.drone_pos = np.array([50.0, 50.0])
        self.user_pos = np.array([
            np.random.uniform(80, 120),
            np.random.uniform(80, 120)
        ])
        
        self.user_movement = UserMovement()
        self.user_movement.current_speed = 0  # 静止目标用于测试
        
        self.step_count = 0
        self.drone_trajectory = [self.drone_pos.copy()]
        self.user_trajectory = [self.user_pos.copy()]
        
        return self._get_state()

    def _calculate_distance(self):
        """计算距离"""
        return np.linalg.norm(self.drone_pos - self.user_pos)
    
    def _execute_drone_action(self, action_angle):
        """执行无人机动作"""
        vx = self.drone_speed * np.cos(action_angle)
        vy = self.drone_speed * np.sin(action_angle)
        
        new_x = self.drone_pos[0] + vx * self.time_step
        new_y = self.drone_pos[1] + vy * self.time_step
        
        new_x = np.clip(new_x, 0, self.area_size)
        new_y = np.clip(new_y, 0, self.area_size)
        
        return np.array([new_x, new_y])

    def step(self, action):
        """执行一步"""
        prev_distance = self._calculate_distance()
        
        # 执行动作
        self.drone_pos = self._execute_drone_action(action[0])
        
        # 更新用户位置（测试版本中保持静止）
        # self.user_pos = np.array(self.user_movement.update_position(self.user_pos))
        
        # 记录轨迹
        self.drone_trajectory.append(self.drone_pos.copy())
        self.user_trajectory.append(self.user_pos.copy())
        
        # 计算奖励
        current_distance = self._calculate_distance()
        reward = self._calculate_reward(prev_distance, current_distance)
        done = self._check_done(current_distance)
        
        self.step_count += 1
        
        return self._get_state(), reward, done
    
    def _calculate_reward(self, prev_distance, current_distance):
        """计算奖励"""
        reward = 0
        
        # 距离奖励
        distance_reward = 2.0 * (prev_distance - current_distance)
        reward += distance_reward
        
        # 时间惩罚
        reward -= 0.1
        
        # 成功奖励
        if current_distance <= self.visit_threshold:
            reward += 100
            
        # 超时惩罚
        if self.step_count >= self.max_steps:
            reward -= 20
            
        # 接近奖励
        if current_distance < 20:
            reward += 1.0
            
        return reward
    
    def _check_done(self, current_distance):
        """检查终止条件"""
        if current_distance <= self.visit_threshold:
            return True
        if self.step_count >= self.max_steps:
            return True
        return False

    def render(self):
        """渲染轨迹"""
        plt.figure(figsize=(8, 8))
        
        drone_traj = np.array(self.drone_trajectory)
        user_traj = np.array(self.user_trajectory)
        
        plt.plot(drone_traj[:, 0], drone_traj[:, 1], 'b-', linewidth=2, label='Drone')
        plt.plot(user_traj[:, 0], user_traj[:, 1], 'r-', linewidth=2, label='User')
        
        plt.plot(drone_traj[0, 0], drone_traj[0, 1], 'go', markersize=10, label='Start')
        plt.plot(drone_traj[-1, 0], drone_traj[-1, 1], 'bs', markersize=10, label='End')
        plt.plot(user_traj[-1, 0], user_traj[-1, 1], 'rs', markersize=10)
        
        # 访问阈值圆圈
        circle = plt.Circle(user_traj[-1], self.visit_threshold, fill=False, color='orange', linestyle='--')
        plt.gca().add_patch(circle)
        
        plt.title(f'Steps: {self.step_count}, Distance: {self._calculate_distance():.2f}m')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.legend()
        plt.xlim(0, self.area_size)
        plt.ylim(0, self.area_size)
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.show()

# 简单测试
if __name__ == "__main__":
    print("Testing Drone Tracking Environment...")
    
    env = DroneTrackingEnv()
    
    # 测试环境
    state = env.reset()
    print(f"Initial state shape: {state.shape}")
    print(f"Initial distance: {env._calculate_distance():.2f}m")
    
    # 随机测试几步
    for i in range(10):
        action = np.random.uniform(-np.pi, np.pi, size=(1,))
        next_state, reward, done = env.step(action)
        print(f"Step {i+1}: Distance={env._calculate_distance():.2f}m, Reward={reward:.2f}, Done={done}")
        
        if done:
            print("Episode finished!")
            break
    
    # 渲染轨迹
    env.render()
    
    print("Test completed!")
