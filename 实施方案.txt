# DDPG无人机单目标追踪详细实施方案

## 1. 问题定义与环境设置

### 1.1 任务描述

- 无人机从起始点追踪一个移动用户
- 无人机进入用户10米范围内即算访问成功
- 目标：最短时间/路径完成追踪任务

### 1.2 物理参数

- **无人机速度**：20m/s，每步移动1m
- **用户速度**：最大3m/s，每步移动0.15m
- **时间步长**：0.05秒/步
- **活动区域**：200m × 200m
- **访问阈值**：10米

### 1.3 坐标系统

- 原点(0,0)位于区域左下角
- 无人机起始位置：(50, 50)
- 用户初始位置：在(100,100)附近随机分布
- 边界处理：碰撞反弹

## 2. 状态空间设计

### 2.1 状态向量 (6维)

```python
state = [
    x_drone,        # 无人机x坐标 [0, 200]
    y_drone,        # 无人机y坐标 [0, 200]  
    dx_relative,    # 用户相对x位置 [-200, 200]
    dy_relative,    # 用户相对y位置 [-200, 200]
    dvx_user,       # 用户x方向速度 [-3, 3]
    dvy_user        # 用户y方向速度 [-3, 3]
]
```

### 2.2 状态归一化

```python
# 位置归一化到[-1, 1]
x_drone_norm = (x_drone - 100) / 100
y_drone_norm = (y_drone - 100) / 100
dx_relative_norm = dx_relative / 200
dy_relative_norm = dy_relative / 200

# 速度归一化到[-1, 1]  
dvx_user_norm = dvx_user / 3
dvy_user_norm = dvy_user / 3
```

## 3. 动作空间设计

### 3.1 动作定义

- **动作维度**：1维连续
- **动作范围**：θ ∈ [-π, π] (航向角)
- **速度固定**：20m/s

### 3.2 动作执行

```python
def execute_action(current_pos, action_angle):
    vx = 20 * np.cos(action_angle)  # 20m/s * cos(θ)
    vy = 20 * np.sin(action_angle)  # 20m/s * sin(θ)
    
    # 时间步长0.05s，移动距离1m
    new_x = current_pos[0] + vx * 0.05
    new_y = current_pos[1] + vy * 0.05
    
    return (new_x, new_y)
```

## 4. 用户移动模型

### 4.1 带惯性的随机移动

```python
class UserMovement:
    def __init__(self):
        self.current_angle = np.random.uniform(0, 2*np.pi)
        self.current_speed = np.random.uniform(1, 3)
        
    def update_position(self, current_pos):
        # 方向惯性 + 随机扰动
        angle_noise = np.random.normal(0, 0.3)  # 标准差0.3弧度
        self.current_angle += angle_noise
        
        # 速度随机变化
        speed_noise = np.random.normal(0, 0.5)
        self.current_speed = np.clip(self.current_speed + speed_noise, 0.5, 3)
        
        # 计算移动距离（0.05秒内的移动）
        move_distance = self.current_speed * 0.05
        
        # 更新位置
        new_x = current_pos[0] + move_distance * np.cos(self.current_angle)
        new_y = current_pos[1] + move_distance * np.sin(self.current_angle)
        
        # 边界反弹
        if new_x <= 0 or new_x >= 200:
            self.current_angle = np.pi - self.current_angle
            new_x = np.clip(new_x, 0, 200)
        if new_y <= 0 or new_y >= 200:
            self.current_angle = -self.current_angle  
            new_y = np.clip(new_y, 0, 200)
            
        return (new_x, new_y)
```

## 5. 奖励函数设计

### 5.1 奖励组成

```python
def calculate_reward(prev_distance, current_distance, is_success, is_timeout, step_count):
    reward = 0
    
    # 1. 距离奖励（主要驱动力）
    distance_reward = 2.0 * (prev_distance - current_distance)
    reward += distance_reward
    
    # 2. 时间惩罚（鼓励快速完成）
    time_penalty = -0.1
    reward += time_penalty
    
    # 3. 成功奖励
    if is_success:
        reward += 100
        
    # 4. 超时惩罚  
    if is_timeout:
        reward -= 20
        
    # 5. 效率奖励（可选）
    if current_distance < 20:  # 接近目标时给额外奖励
        reward += 1.0
        
    return reward
```

### 5.2 奖励设计原理

- **距离奖励**：主要驱动力，鼓励靠近目标
- **时间惩罚**：防止无意义游走
- **成功奖励**：强化完成任务的价值
- **效率奖励**：精细化接近阶段的行为

## 6. 环境实现

### 6.1 环境类结构

```python
class DroneTrackingEnv:
    def __init__(self):
        self.drone_pos = None
        self.user_pos = None
        self.user_movement = UserMovement()
        self.step_count = 0
        self.max_steps = 2000  # 100秒限制
        
    def reset(self):
        # 初始化位置
        self.drone_pos = (50, 50)
        self.user_pos = (
            np.random.uniform(80, 120),
            np.random.uniform(80, 120)  
        )
        self.step_count = 0
        return self._get_state()
        
    def step(self, action):
        # 执行动作
        self.drone_pos = self._execute_drone_action(action)
        
        # 更新用户位置
        self.user_pos = self.user_movement.update_position(self.user_pos)
        
        # 计算奖励和终止条件
        distance = self._calculate_distance()
        reward = self._calculate_reward(distance)
        done = self._check_done(distance)
        
        self.step_count += 1
        
        return self._get_state(), reward, done, {}
```

## 7. DDPG网络架构

### 7.1 Actor网络

```python
class Actor(nn.Module):
    def __init__(self, state_dim=6, action_dim=1, hidden_dim=256):
        super().__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc4 = nn.Linear(hidden_dim, action_dim)
        
    def forward(self, state):
        x = torch.relu(self.fc1(state))
        x = torch.relu(self.fc2(x))
        x = torch.relu(self.fc3(x))
        x = torch.tanh(self.fc4(x)) * np.pi  # 输出[-π, π]
        return x
```

### 7.2 Critic网络

```python
class Critic(nn.Module):
    def __init__(self, state_dim=6, action_dim=1, hidden_dim=256):
        super().__init__()
        self.fc1 = nn.Linear(state_dim + action_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc4 = nn.Linear(hidden_dim, 1)
        
    def forward(self, state, action):
        x = torch.cat([state, action], dim=1)
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = torch.relu(self.fc3(x))
        x = self.fc4(x)
        return x
```

## 8. 训练参数设置

### 8.1 DDPG超参数

```python
HYPERPARAMS = {
    'lr_actor': 1e-4,
    'lr_critic': 1e-3, 
    'gamma': 0.99,
    'tau': 0.005,           # 软更新参数
    'buffer_size': 100000,
    'batch_size': 64,
    'noise_std': 0.2,       # OU噪声标准差
    'noise_decay': 0.995,   # 噪声衰减
    'update_freq': 1        # 每步都更新
}
```

### 8.2 训练流程

```python
TRAINING_CONFIG = {
    'max_episodes': 5000,
    'max_steps_per_episode': 2000,
    'random_episodes': 100,     # 纯随机探索
    'save_freq': 500,           # 模型保存频率
    'eval_freq': 100,           # 评估频率
    'eval_episodes': 10         # 评估轮数
}
```

## 9. 分阶段训练策略

### 9.1 第一阶段：静止目标 (Episodes 1-1000)

- 用户位置固定，验证基本追踪能力
- 成功率达到95%以上进入下一阶段

### 9.2 第二阶段：慢速移动 (Episodes 1001-2500)

- 用户移动速度限制在1m/s
- 学习预测和拦截策略

### 9.3 第三阶段：正常速度 (Episodes 2501-5000)

- 用户移动速度恢复到3m/s
- 完整的追踪任务

## 10. 评估指标


- **成功率**：每100个episode的成功访问比例
- **平均步数**：成功episode的平均完成步数
- **平均奖励**：episode累积奖励
- **运动轨迹绘图**：无人机的轨迹和用户的轨迹
