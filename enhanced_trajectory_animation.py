"""
增强版轨迹动画生成器
====================

功能：
- 同时显示无人机和用户的运动轨迹动画
- 生成包含距离变化的双面板动画
- 创建静态分析图表
- 所有参数可在文件顶部配置区域轻松调整

使用方法：
1. 调整下方配置参数区域的参数
2. 运行脚本：python enhanced_trajectory_animation.py
3. 查看生成的GIF动画和分析图表

参数说明：
- ANIMATION_INTERVAL: 动画帧间隔（毫秒），数值越小动画越快
- ANIMATION_FPS: GIF帧率
- 颜色参数: 可自定义轨迹线和点的颜色
- 文件路径: 可修改输入和输出文件名
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
import os

# ==================== 配置参数 ====================
# 文件路径配置episode_validation_16_uav_trajectory
USER_TRAJECTORY_FILE = 'episode_validation_5_user_trajectory.txt'
UAV_TRAJECTORY_FILE = 'episode_validation_5_uav_trajectory.txt'
OUTPUT_GIF_FILE = 'enhanced_trajectory_animation.gif'
OUTPUT_ANALYSIS_FILE = 'trajectory_analysis.png'

# 动画参数
ANIMATION_INTERVAL = 50  # 毫秒，控制动画速度（数值越小越快）
ANIMATION_FPS = 5         # GIF帧率
ANIMATION_DPI = 100       # GIF分辨率
ANIMATION_REPEAT = True   # 是否循环播放

# 图形参数
FIGURE_SIZE_ANIMATION = (16, 8)    # 动画图形大小
FIGURE_SIZE_ANALYSIS = (16, 12)    # 静态分析图形大小
COORDINATE_MARGIN = 5              # 坐标轴边距

# 轨迹线条参数
USER_LINE_COLOR = 'blue'
UAV_LINE_COLOR = 'red'
CONNECTION_LINE_COLOR = 'green'
DISTANCE_LINE_COLOR = 'purple'
LINE_WIDTH = 2
CONNECTION_LINE_WIDTH = 1
LINE_ALPHA = 0.7
CONNECTION_LINE_ALPHA = 0.5

# 点标记参数
USER_POINT_COLOR = 'blue'
UAV_POINT_COLOR = 'red'
POINT_RADIUS = 2
POINT_ALPHA = 0.8
MARKER_SIZE = 100

# 文本和字体参数
FONT_SIZE_TITLE = 14
FONT_SIZE_LABEL = 12
FONT_SIZE_LEGEND = 10
FONT_SIZE_INFO = 10
GRID_ALPHA = 0.3

# 颜色配置
INFO_BOX_COLOR = 'lightblue'
STATS_BOX_COLOR = 'lightgreen'
HISTOGRAM_COLOR = 'orange'
BOX_ALPHA = 0.8

# 统计分析参数
HISTOGRAM_BINS = 20
DECIMAL_PLACES = 2

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_trajectory(filename):
    """加载轨迹数据"""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                coords = line.split()
                if len(coords) == 2:
                    x, y = float(coords[0]), float(coords[1])
                    data.append([x, y])
    return np.array(data)

def create_enhanced_trajectory_animation():
    """创建增强版轨迹动画"""
    # 加载数据
    user_trajectory = load_trajectory(USER_TRAJECTORY_FILE)
    uav_trajectory = load_trajectory(UAV_TRAJECTORY_FILE)

    print(f"User trajectory points: {len(user_trajectory)}")
    print(f"UAV trajectory points: {len(uav_trajectory)}")

    # 确定坐标范围
    all_x = np.concatenate([user_trajectory[:, 0], uav_trajectory[:, 0]])
    all_y = np.concatenate([user_trajectory[:, 1], uav_trajectory[:, 1]])

    x_min, x_max = all_x.min() - COORDINATE_MARGIN, all_x.max() + COORDINATE_MARGIN
    y_min, y_max = all_y.min() - COORDINATE_MARGIN, all_y.max() + COORDINATE_MARGIN

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=FIGURE_SIZE_ANIMATION)
    
    # 主动画图
    ax1.set_xlim(30, 180)
    ax1.set_ylim(30, 180)
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=GRID_ALPHA)
    ax1.set_xlabel('X Coordinate', fontsize=FONT_SIZE_LABEL)
    ax1.set_ylabel('Y Coordinate', fontsize=FONT_SIZE_LABEL)
    ax1.set_title('UAV and User Trajectory Animation', fontsize=FONT_SIZE_TITLE, fontweight='bold')

    # 距离变化图
    ax2.set_xlabel('Time Step', fontsize=FONT_SIZE_LABEL)
    ax2.set_ylabel('Distance', fontsize=FONT_SIZE_LABEL)
    ax2.set_title('Distance Between UAV and User', fontsize=FONT_SIZE_TITLE, fontweight='bold')
    ax2.grid(True, alpha=GRID_ALPHA)
    
    # 计算所有时间步的距离
    distances = []
    for i in range(min(len(user_trajectory), len(uav_trajectory))):
        dist = np.sqrt((user_trajectory[i, 0] - uav_trajectory[i, 0])**2 + 
                      (user_trajectory[i, 1] - uav_trajectory[i, 1])**2)
        distances.append(dist)
    
    distances = np.array(distances)
    ax2.set_xlim(0, len(distances))
    ax2.set_ylim(0, max(distances) * 1.1)
    
    # 初始化轨迹线
    user_line, = ax1.plot([], [], '-', color=USER_LINE_COLOR, linewidth=LINE_WIDTH,
                         label='User Trajectory', alpha=LINE_ALPHA)
    uav_line, = ax1.plot([], [], '-', color=UAV_LINE_COLOR, linewidth=LINE_WIDTH,
                        label='UAV Trajectory', alpha=LINE_ALPHA)
    connection_line, = ax1.plot([], [], '--', color=CONNECTION_LINE_COLOR,
                               linewidth=CONNECTION_LINE_WIDTH,
                               alpha=CONNECTION_LINE_ALPHA, label='Connection')

    # 初始化当前位置点
    user_point = Circle((0, 0), POINT_RADIUS, color=USER_POINT_COLOR,
                       alpha=POINT_ALPHA, zorder=5)
    uav_point = Circle((0, 0), POINT_RADIUS, color=UAV_POINT_COLOR,
                      alpha=POINT_ALPHA, zorder=5)
    ax1.add_patch(user_point)
    ax1.add_patch(uav_point)

    # 距离图线
    distance_line, = ax2.plot([], [], color=DISTANCE_LINE_COLOR, linewidth=LINE_WIDTH)
    current_distance_point, = ax2.plot([], [], 'ro', markersize=8)

    # 添加图例
    ax1.legend(loc='upper right', fontsize=FONT_SIZE_LEGEND)
    
    # 添加信息文本
    info_text = ax1.text(0.02, 0.98, '', transform=ax1.transAxes,
                        verticalalignment='top', fontsize=FONT_SIZE_INFO,
                        bbox=dict(boxstyle='round', facecolor=INFO_BOX_COLOR, alpha=BOX_ALPHA))

    # 统计信息文本
    stats_text = ax2.text(0.02, 0.98, '', transform=ax2.transAxes,
                         verticalalignment='top', fontsize=FONT_SIZE_INFO,
                         bbox=dict(boxstyle='round', facecolor=STATS_BOX_COLOR, alpha=BOX_ALPHA))
    
    def animate(frame):
        """动画函数"""
        # 计算当前帧对应的数据点数量
        max_points = min(len(user_trajectory), len(uav_trajectory))
        current_points = min(frame + 1, max_points)
        
        if current_points > 0:
            # 更新轨迹线
            user_line.set_data(user_trajectory[:current_points, 0], 
                             user_trajectory[:current_points, 1])
            uav_line.set_data(uav_trajectory[:current_points, 0], 
                            uav_trajectory[:current_points, 1])
            
            # 更新当前位置点
            current_idx = current_points - 1
            user_pos = (user_trajectory[current_idx, 0], user_trajectory[current_idx, 1])
            uav_pos = (uav_trajectory[current_idx, 0], uav_trajectory[current_idx, 1])
            
            user_point.center = user_pos
            uav_point.center = uav_pos
            
            # 更新连接线
            connection_line.set_data([user_pos[0], uav_pos[0]], [user_pos[1], uav_pos[1]])
            
            # 计算当前距离
            current_distance = distances[current_idx]
            
            # 更新距离图
            distance_line.set_data(range(current_points), distances[:current_points])
            current_distance_point.set_data([current_idx], [current_distance])
            
            # 更新信息文本
            info_text.set_text(f'Time Step: {current_points}/{max_points}\n'
                              f'User Position: ({user_pos[0]:.{DECIMAL_PLACES}f}, {user_pos[1]:.{DECIMAL_PLACES}f})\n'
                              f'UAV Position: ({uav_pos[0]:.{DECIMAL_PLACES}f}, {uav_pos[1]:.{DECIMAL_PLACES}f})\n'
                              f'Current Distance: {current_distance:.{DECIMAL_PLACES}f}')

            # 更新统计信息
            if current_points > 1:
                avg_distance = np.mean(distances[:current_points])
                min_distance = np.min(distances[:current_points])
                max_distance = np.max(distances[:current_points])

                stats_text.set_text(f'Distance Statistics:\n'
                                   f'Current: {current_distance:.{DECIMAL_PLACES}f}\n'
                                   f'Average: {avg_distance:.{DECIMAL_PLACES}f}\n'
                                   f'Min: {min_distance:.{DECIMAL_PLACES}f}\n'
                                   f'Max: {max_distance:.{DECIMAL_PLACES}f}')
        
        return (user_line, uav_line, connection_line, user_point, uav_point, 
                distance_line, current_distance_point, info_text, stats_text)
    
    # 创建动画
    max_frames = min(len(user_trajectory), len(uav_trajectory))
    anim = animation.FuncAnimation(fig, animate, frames=max_frames,
                                 interval=ANIMATION_INTERVAL, blit=False,
                                 repeat=ANIMATION_REPEAT)

    # 保存为GIF
    print("Generating enhanced GIF animation...")
    anim.save(OUTPUT_GIF_FILE, writer='pillow', fps=ANIMATION_FPS, dpi=ANIMATION_DPI)
    print(f"Enhanced GIF animation saved as '{OUTPUT_GIF_FILE}'")
    
    # 显示动画
    plt.tight_layout()
    plt.show()
    
    return anim

def create_static_analysis():
    """创建静态分析图"""
    # 加载数据
    user_trajectory = load_trajectory(USER_TRAJECTORY_FILE)
    uav_trajectory = load_trajectory(UAV_TRAJECTORY_FILE)
    
    # 计算距离
    distances = []
    for i in range(min(len(user_trajectory), len(uav_trajectory))):
        dist = np.sqrt((user_trajectory[i, 0] - uav_trajectory[i, 0])**2 + 
                      (user_trajectory[i, 1] - uav_trajectory[i, 1])**2)
        distances.append(dist)
    
    distances = np.array(distances)
    
    # 创建静态分析图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=FIGURE_SIZE_ANALYSIS)

    # 完整轨迹图
    ax1.plot(user_trajectory[:, 0], user_trajectory[:, 1], '-', color=USER_LINE_COLOR,
             linewidth=LINE_WIDTH, label='User Trajectory', alpha=LINE_ALPHA)
    ax1.plot(uav_trajectory[:, 0], uav_trajectory[:, 1], '-', color=UAV_LINE_COLOR,
             linewidth=LINE_WIDTH, label='UAV Trajectory', alpha=LINE_ALPHA)
    ax1.scatter(user_trajectory[0, 0], user_trajectory[0, 1], color=USER_POINT_COLOR,
                s=MARKER_SIZE, marker='o', label='User Start', zorder=5)
    ax1.scatter(uav_trajectory[0, 0], uav_trajectory[0, 1], color=UAV_POINT_COLOR,
                s=MARKER_SIZE, marker='o', label='UAV Start', zorder=5)
    ax1.scatter(user_trajectory[-1, 0], user_trajectory[-1, 1], color=USER_POINT_COLOR,
                s=MARKER_SIZE, marker='s', label='User End', zorder=5)
    ax1.scatter(uav_trajectory[-1, 0], uav_trajectory[-1, 1], color=UAV_POINT_COLOR,
                s=MARKER_SIZE, marker='s', label='UAV End', zorder=5)
    ax1.set_xlabel('X Coordinate', fontsize=FONT_SIZE_LABEL)
    ax1.set_ylabel('Y Coordinate', fontsize=FONT_SIZE_LABEL)
    ax1.set_title('Complete Trajectories', fontsize=FONT_SIZE_TITLE)
    ax1.legend(fontsize=FONT_SIZE_LEGEND)
    ax1.grid(True, alpha=GRID_ALPHA)
    ax1.set_aspect('equal')

    # 距离变化图
    ax2.plot(distances, color=DISTANCE_LINE_COLOR, linewidth=LINE_WIDTH)
    ax2.set_xlabel('Time Step', fontsize=FONT_SIZE_LABEL)
    ax2.set_ylabel('Distance', fontsize=FONT_SIZE_LABEL)
    ax2.set_title('Distance Between UAV and User Over Time', fontsize=FONT_SIZE_TITLE)
    ax2.grid(True, alpha=GRID_ALPHA)

    # 距离统计直方图
    ax3.hist(distances, bins=HISTOGRAM_BINS, alpha=LINE_ALPHA,
             color=HISTOGRAM_COLOR, edgecolor='black')
    ax3.set_xlabel('Distance', fontsize=FONT_SIZE_LABEL)
    ax3.set_ylabel('Frequency', fontsize=FONT_SIZE_LABEL)
    ax3.set_title('Distance Distribution', fontsize=FONT_SIZE_TITLE)
    ax3.grid(True, alpha=GRID_ALPHA)
    
    # 速度分析
    user_speeds = np.sqrt(np.diff(user_trajectory[:, 0])**2 + np.diff(user_trajectory[:, 1])**2)
    uav_speeds = np.sqrt(np.diff(uav_trajectory[:, 0])**2 + np.diff(uav_trajectory[:, 1])**2)

    ax4.plot(user_speeds, '-', color=USER_LINE_COLOR, linewidth=LINE_WIDTH,
             label='User Speed', alpha=LINE_ALPHA)
    ax4.plot(uav_speeds, '-', color=UAV_LINE_COLOR, linewidth=LINE_WIDTH,
             label='UAV Speed', alpha=LINE_ALPHA)
    ax4.set_xlabel('Time Step', fontsize=FONT_SIZE_LABEL)
    ax4.set_ylabel('Speed', fontsize=FONT_SIZE_LABEL)
    ax4.set_title('Speed Comparison', fontsize=FONT_SIZE_TITLE)
    ax4.legend(fontsize=FONT_SIZE_LEGEND)
    ax4.grid(True, alpha=GRID_ALPHA)

    plt.tight_layout()
    plt.savefig(OUTPUT_ANALYSIS_FILE, dpi=ANIMATION_DPI*3, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("\n=== Trajectory Analysis ===")
    print(f"Total time steps: {len(distances)}")
    print(f"Average distance: {np.mean(distances):.{DECIMAL_PLACES}f}")
    print(f"Minimum distance: {np.min(distances):.{DECIMAL_PLACES}f}")
    print(f"Maximum distance: {np.max(distances):.{DECIMAL_PLACES}f}")
    print(f"Distance standard deviation: {np.std(distances):.{DECIMAL_PLACES}f}")
    print(f"Average user speed: {np.mean(user_speeds):.{DECIMAL_PLACES}f}")
    print(f"Average UAV speed: {np.mean(uav_speeds):.{DECIMAL_PLACES}f}")

if __name__ == "__main__":
    # 检查文件是否存在
    if not os.path.exists(USER_TRAJECTORY_FILE):
        print(f"Error: Cannot find {USER_TRAJECTORY_FILE} file")
        exit(1)
    if not os.path.exists(UAV_TRAJECTORY_FILE):
        print(f"Error: Cannot find {UAV_TRAJECTORY_FILE} file")
        exit(1)
    
    # 创建增强动画
    print("Creating enhanced animation...")
    animation_obj = create_enhanced_trajectory_animation()
    
    # 创建静态分析
    print("\nCreating static analysis...")
    create_static_analysis()
